# .env.development
NODE_ENV=development
VUE_APP_ENV=development

#数字马院没有自己的接口，暂不定义
VUE_APP_API_BASE= ''

#
#一、理论教学获相关配置，必须保证环境一致
#
#1.获取课程列表
VUE_APP_COURSE_API = https://courseapi.ulearning.cn
#2.课程详情页跳转
VUE_APP_COURSE_DETAIL_HOST = https://umooc.ulearning.cn

#
#二、OBS专用
#
VUE_APP_OBS_API = https://courseapi.tongshike.cn

#
#三、主题精品课相关配置，必须保证环境一致
#
#1.获取课程列表
VUE_APP_JPK_HOST = https://sjjxapi.ulearning.cn
#2.课程详情页跳转
VUE_APP_JPK_DETAIL_HOST = https://sjjx.moocpeople.cn

#
#四、人民公开课的接口
#
VUE_APP_GKK_HOST = https://gkkapi.tongshike.cn

#
#五、与用户登录相关的配置，必须保证环境一致
#
#1.登录、用户检查用的接口ip
VUE_APP_USER_LOGIN_API = https://courseapi.tongshike.cn
#2.登录页-忘记密码/注册跳转链接
VUE_APP_ULEARNING_LOGIN_HOST = https://umobile.tongshike.cn
#3.思政登录接口专用
VUE_APP_LOGIN_HOST = https://szaccountapi.tongshike.cn
#4.登录后重定向链接
VUE_APP_LOGIN_REDIRECT_URL = https://app.tongshike.cn
#5.登录页的链接，因为生产环境有专门的登录页，开发环境、测试环境的登录是为了方便开发测试新写的
VUE_APP_LOGIN_URL = /#/login
#6.数字马院思政智能体相关接口ip
VUE_APP_CLOUDSEARCH_API = https://cloudsearchapi.tongshike.cn
#7.嵌入AI的host
VUE_APP_AI_HOST = https://ai.tongshike.cn/assistant
#8.数字马院思政智能体系统的免登录跳转链接
VUE_APP_SZMY_AI_HOST = https://szmy.ai.tongshike.cn
#9.实践教学系统免登录跳转链接
VUE_APP_SZMY_SJJX_HOST = https://sjjx.tongshike.cn
#10.是否采用重定向的登录方式
VUE_APP_IS_REDIRECT_LOGIN = false