import User from "@/model/User.js";
import cookies from "@/utils/cookie";
export default {
  namespaced: true,
  state: {
    user: new User({}),
  },
  getters: {
    user(state) {
      return state.user;
    },
    token(state) {
      return state.user.authorization;
    },
  },
  mutations: {
    setUser(state, user) {
      state.user = new User(user);
    },
  },
  actions: {
    setUser({ commit }) {
      return new Promise((resolve, reject) => {
        let user = cookies.get("USERINFO");
        if (user) {
          const data = JSON.parse(user);
          commit("setUser", data);
          resolve(user);
        } else {
          reject("加载用户失败");
        }
      });
    },
  },
};
