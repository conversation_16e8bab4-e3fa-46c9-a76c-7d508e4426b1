// directives/animate.js
export default {
  inserted(el) {
    el.style.opacity = 0;
    el.style.transform = "translateY(30px)";
    el.style.transition = "opacity 0.6s ease, transform 0.6s ease";

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            el.style.opacity = 1;
            el.style.transform = "translateY(0)";
            observer.unobserve(el);
          }
        });
      },
      { threshold: 0.1 }
    );

    observer.observe(el);
    el._animateObserver = observer;
  },
  unbind(el) {
    if (el._animateObserver) {
      el._animateObserver.disconnect();
      delete el._animateObserver;
    }
  },
};
