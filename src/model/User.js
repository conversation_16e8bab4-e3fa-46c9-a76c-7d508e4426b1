export default class User {
  constructor({
    loginName = "",
    name = "",
    sex = 1,
    orgId,
    roleId,
    headimage = "",
    userId,
    authorization = "",
    role,
    avatar = "",
    userID,
    token = "",
    cellphone,
    institutionName,
    signature,
    studentid,
  }) {
    this.loginName = loginName;
    this.name = name;
    this.sex = sex;
    this.orgId = orgId || -1;
    this.roleId = parseInt(roleId || role || 9);
    this.avatar = headimage || avatar;
    this.userId = userId || userID;
    this.authorization = authorization || token;
    this.cellphone = cellphone || null;
    this.institutionName = institutionName || "";
    this.signature = signature || "";
    this.studentid = studentid || "";
  }
}
