import Vue from "vue";
import "@/styles/index.scss";
import {
  Button,
  Select,
  Input,
  Avatar,
  Dropdown,
  DropdownMenu,
  DropdownItem,
  Carousel,
  CarouselItem,
  Image,
  Menu,
  Submenu,
  MenuItem,
  Option,
  Tooltip,
  Progress,
  Message,
  Scrollbar,
  Drawer,
  Pagination,
  Form,
  FormItem,
  Loading
} from "element-ui";
import App from "./App.vue";
import router from "./router";
import store from "./store";
import { check_login_status } from "@/utils/login";

import "@/assets/icons/index";
import animate from "./directives/animate";
Vue.directive("animate", animate);

Vue.config.productionTip = false;

Vue.component(Button.name, Button);
Vue.component(Select.name, Select);
Vue.component(Input.name, Input);
Vue.component(Avatar.name, Avatar);
Vue.component(Dropdown.name, Dropdown);
Vue.component(DropdownMenu.name, DropdownMenu);
Vue.component(DropdownItem.name, DropdownItem);
Vue.component(Carousel.name, Carousel);
Vue.component(CarouselItem.name, CarouselItem);
Vue.component(Image.name, Image);
Vue.component(Menu.name, Menu);
Vue.component(Submenu.name, Submenu);
Vue.component(MenuItem.name, MenuItem);
Vue.component(Option.name, Option);
Vue.component(Tooltip.name, Tooltip);
Vue.component(Progress.name, Progress);
Vue.component(Scrollbar.name, Scrollbar);
Vue.component(Drawer.name, Drawer);
Vue.component(Pagination.name, Pagination);
Vue.component(Form.name, Form);
Vue.component(FormItem.name, FormItem);
Vue.use(Loading.directive);
Vue.prototype.$message = Message;

check_login_status();

Vue.prototype.$sjjxDocumentIdName = "sjjxDocument";

new Vue({
  router,
  store,
  render: (h) => h(App),
}).$mount("#app");
