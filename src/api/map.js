import axios from "axios";
import request from "@/utils/axios";
const { VUE_APP_GKK_HOST } = process.env;
// 实践教学地图
export function apiGetPracticeProvince() {
  return request({
    url: `${VUE_APP_GKK_HOST}/portal/teach/map/province`,
    method: "get",
  });
}

// 实践教学地图基地列表
export function apiGetPracticeMap(params) {
  return request({
    url: `${VUE_APP_GKK_HOST}/portal/teach/map`,
    method: "get",
    params,
  });
}

/************************* 地图 **********************/
export function getChinaMap() {
  return axios.get(
    "/100000_full.json"
  );
}
