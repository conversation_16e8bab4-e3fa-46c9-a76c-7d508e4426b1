import request from "@/utils/axios";
const { VUE_APP_CLOUDSEARCH_API, VUE_APP_COURSE_API, VUE_APP_JPK_HOST,VUE_APP_LOGIN_HOST,VUE_APP_USER_LOGIN_API } = process.env;

export function getPortalOrgInfo(params) {
  return request({
    url: `${VUE_APP_CLOUDSEARCH_API}/sz/portal/org/info`,
    method: "get",
    params,
  });
}

export function getAIConfigInfo(params) {
  return request({
    url: `${VUE_APP_CLOUDSEARCH_API}/sz/portal/aside/config`,
    method: "get",
    params,
  });
}

export function getCoursesOverviewInfo(params) {
  return request({
    url: `${VUE_APP_COURSE_API}/homepage/courses/overview/info`,
    method: "get",
    params,
  });
}

export function getCoursesDetailInfo(id) {
  return request({
    url: `${VUE_APP_COURSE_API}/homepage/courses/detail/${id}`,
    method: "get",
  });
}

export function usersCheck(params) {
  return request({
    url: `${VUE_APP_USER_LOGIN_API}/users/check`,
    method: "get",
    params,
  });
}

// 获取示范课程
export function apiGetCourseList(data) {
  return request({
    url: `${VUE_APP_CLOUDSEARCH_API}/theoryTeach/model/course`,
    method: "post",
    data,
  });
}

//获取主题精品课
export function getPremiumCourses() {
  return request({
    url: `${VUE_APP_JPK_HOST}/homepage/publicCourses?seclassifyId=4&orgId=9181`,
    method: "get",
  });
}

//登录
export function sz_login(data) {
  return request({
    url: `${VUE_APP_LOGIN_HOST}/uc/login?source=sa`,
    method: "post",
    data,
  });
}
