<template>
  <div class="flex-center app-footer">
    <div class="layout-content flex-end-between">
      <div>
        <img src="@/assets/images/logo.png" alt="" class="logo" />
        <div class="font_small">服务热线：400-610-7808</div>
        <div class="font_small">
          友情链接：<a href="http://www.people.cn/" target="_blank">人民网</a> |
          <a href="http://www.xuexi.cn/" target="_blank">学习强国</a> |
          <a href="http://www.cctv.com/" target="_blank">央视网</a> |
          <a href="https://www.ulearning.com.cn/" target="_blank">文华在线</a> |
          <a href="http://www.ulearning.cn/" target="_blank">优学院</a>
        </div>
        <div class="font_small">技术提供: 北京文华在线教育科技股份有限公司</div>
      </div>
      <div>
        <div class="font_small font-small-grey">
          mooc.people.cn 人民网版权所有，未经书面授权禁止使用
        </div>
        <div class="font_small font-small-grey">
          Copyright@2016-2023 by mooc.people.cn All rights reserved
        </div>
        <div class="font_small font-small-grey">京ICP备17007657号-8</div>
      </div>
      <div class="qrcode-box">
        <img src="@/assets/images/qrcode.png" alt="" class="qrcode" />
        <div class="font_small">关注我们</div>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "AppFooter",
};
</script>
<style lang="scss" scoped>
.app-footer {
  min-height: 197px;
  background: #f5f7fa;
  .layout-content {
    .font_small {
      font-weight: 400;
      font-size: 14px;
      color: #475467;
      line-height: 100%;
      letter-spacing: 0%;
      line-height: 20px;
    }
    .font-small-grey {
      color: #98a2b3;
    }
    a {
      color: inherit; /* 继承父元素颜色 */
      text-decoration: none; /* 去除下划线 */
      outline: none; /* 去除点击时的外边框（可选） */
    }
    .logo {
      height: 42px;
      width: auto;
      max-width: 174px;
      margin-bottom: 23px;
    }
    .qrcode-box {
      text-align: center;
      .qrcode {
        height: 120px;
        width: 120px;
        margin-bottom: 5px;
      }
    }
  }
}
</style>
