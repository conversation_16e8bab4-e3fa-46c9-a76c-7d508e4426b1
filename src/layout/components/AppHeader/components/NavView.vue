<template>
  <div class="nav-view">
    <div class="layout-content nav-view-content">
      <el-menu
        :default-active="activeIndex"
        mode="horizontal"
        background-color="var(--primary-color)"
        text-color="#fff"
        active-text-color="#fff"
        class="flex"
      >
        <el-menu-item
          :index="menu.path"
          v-for="menu in navList"
          :key="menu.name"
          @click="handleSelect(menu)"
          >{{ menu.name }}</el-menu-item
        >
      </el-menu>
    </div>
  </div>
</template>

<script>
const MENU_TYPE = {
  route: "route", //路由
  link_open: "link_open", //链接新开页
  link_href: "link_href", //链接当前页
  document: "document", //当前页面定位
  no_login_szai: "no_login_szai", //免登录跳转思政智能体系统
};
import { goToSZMYAi } from "@/utils/no_login_required_for_redirection.js";
export default {
  name: "NavView",
  data() {
    return {
      activeIndex: "/home",
      navList: [
        {
          name: "首页",
          path: "/home",
          type: MENU_TYPE.route,
        },
        {
          name: "思政教学智能体",
          path: `/index.html`,
          type: MENU_TYPE.no_login_szai,
        },
        {
          name: "理论教学",
          path: "/theoreticalteaching",
          type: MENU_TYPE.route,
        },
        {
          name: "实践教学",
          path: this.$sjjxDocumentIdName,
          type: MENU_TYPE.document,
        },
        {
          name: "“数字马院”联盟",
          path: "https://www.shuzimayuan.cn/#/league/index/35",
          type: MENU_TYPE.link_href,
        },
        {
          name: "思政解决方案",
          path: "",
          type: null,
        },
      ],
    };
  },
  watch: {
    $route(to, from) {
      this.activeIndex = to.path;
    },
  },
  created() {
    this.activeIndex = this.$route.path;
  },
  methods: {
    handleSelect(item) {
      if (item.type == MENU_TYPE.route) {
        this.$router.push(item.path);
      } else if (item.type == MENU_TYPE.document) {
        const aimDocument = document.getElementById(item.path);
        if (aimDocument) {
          aimDocument.scrollIntoView({
            behavior: "smooth", // 启用平滑滚动
            block: "start", // 对齐方式
          });
        } else {
          sessionStorage.setItem("SCROLL_TO_AIM_DOCUMENT", item.path);
          setTimeout(() => {
            this.$router.push("/home");
          }, 200);
        }
      } else if (item.type == MENU_TYPE.link_open) {
        window.open(item.path, "_blank");
      } else if (item.type == MENU_TYPE.link_href) {
        window.location.href = item.path;
      } else if (item.type == MENU_TYPE.no_login_szai) {
        goToSZMYAi(item.path);
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.nav-view {
  background-color: var(--color-primary);
  .nav-view-content {
    .el-menu {
      height: 50px;
      border-bottom: 0px;
      overflow: hidden;
      .el-menu-item {
        flex: 1;
        text-align: center;
        height: 50px;
        line-height: 50px;
        border-bottom: 0px !important;
        font-size: 18px;
        font-weight: 600;
        &:hover {
          background-color: #d7a749 !important;
        }
      }
      .is-active {
        background-color: #d7a749 !important;
      }
    }
  }
}
</style>
