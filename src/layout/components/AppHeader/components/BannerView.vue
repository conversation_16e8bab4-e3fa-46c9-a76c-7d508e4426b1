<template>
  <div class="banner-view">
    <el-carousel height="400px">
      <el-carousel-item v-for="item in banners" :key="item.id">
        <el-image
          :src="item.img"
          :title="item.title"
          :alt="item.title"
          fit="cover"
          style="height: 100%; width: 100%"
        ></el-image>
      </el-carousel-item>
    </el-carousel>
  </div>
</template>

<script>
import banner0 from "@/assets/banners/0.png";
import banner1 from "@/assets/banners/1.png";
import banner2 from "@/assets/banners/2.jpg";
import banner3 from "@/assets/banners/3.jpg";
import banner4 from "@/assets/banners/4.png";
import banner5 from "@/assets/banners/5.png";

export default {
  name: "BannerView",
  data() {
    return {
      banners: [
        {
          id: 0,
          img: banner0,
          url: "https://zyk.ulearning.cn/",
          title: "人民大思政课资源库",
        },
        {
          id: 1,
          img: banner1,
          url: "",
          title: "",
        },
        {
          id: 2,
          img: banner2,
          url: "http://www.shuzimayuan.cn/#/league/index/35",
          title: "全国高校马克思主义学院数字化平台建设联盟官方网站",
        },
        {
          id: 3,
          img: banner3,
          url: "http://mooc.people.cn/publicCourse/index.html#/index/hotCourse?topic=keyanhui",
          title: "课研会",
        },
        {
          id: 4,
          img: banner4,
          url: "https://marx.ulearning.cn/digitalweb/prepareResource.do",
          title: "新教材同步备课资源",
        },
        {
          id: 5,
          img: banner5,
          url: "https://redvr.ulearning.cn",
          title: "",
        },
      ],
    };
  },
};
</script>
<style lang="scss" scoped>
.banner-view {
  ::v-deep .el-carousel {
    // .el-carousel__container {
    //   .el-carousel__arrow{}
    // }
    .el-carousel__indicators {
      bottom: 50px;
      .el-carousel__indicator {
        padding: 0px 8px;
        .el-carousel__button {
          height: 8px;
          width: 8px;
          border-radius: 50%;
          opacity: 0.5;
        }
      }
      .is-active {
        .el-carousel__button {
          opacity: 1;
        }
      }
    }
  }
}
</style>
