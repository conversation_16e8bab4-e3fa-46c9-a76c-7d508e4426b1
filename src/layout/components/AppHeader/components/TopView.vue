<template>
  <div class="layout-content flex-center-between top-view">
    <img src="@/assets/images/logo.png" alt="" class="logo" />
    <div>
      <template v-if="token">
        <el-dropdown
          @command="handleCommand"
          @visible-change="visibleChange"
          placement="bottom"
        >
          <div class="flex-center login-header">
            <el-avatar :size="32" :src="user.avatar" @error="errorHandler">
              <img src="@/assets/images/avatar.png" />
            </el-avatar>
            <span class="user-name">{{ user.name }}</span>
            <i
              class="el-icon-caret-bottom el-icon"
              :class="{ 'el-icon__rotate': dropdownVisible }"
            ></i>
          </div>
          <el-dropdown-menu slot="dropdown" class="user-dropdown">
            <el-dropdown-item command="logout">退出</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </template>
      <el-button
        type="primary"
        icon="el-icon-user"
        @click="handleCommand('login')"
        v-else
        >登录</el-button
      >
    </div>
  </div>
</template>

<script>
import { mapState, mapGetters } from "vuex";
import { logout, login } from "@/utils/login";
export default {
  name: "TopView",
  data() {
    return {
      dropdownVisible: false,
    };
  },
  computed: {
    ...mapState("user", ["user"]),
    ...mapGetters("user", ["token"]),
  },
  methods: {
    errorHandler() {
      return true;
    },
    handleCommand(command) {
      switch (command) {
        case "logout":
          logout();
          break;
        case "login":
          login();
          break;
        default:
          break;
      }
    },
    visibleChange(visible) {
      this.dropdownVisible = visible;
    },
  },
};
</script>
<style lang="scss" scoped>
.top-view {
  height: 80px;
  .logo {
    height: 42px;
    width: auto;
    max-width: 174px;
  }
  .el-dropdown {
    .login-header {
      cursor: pointer;
      .user-name {
        margin: 0 3px;
        color: #182230;
      }
      .el-icon {
        transition: all 0.3s;
        font-size: 12px;
        &__rotate {
          transform: rotate(180deg);
        }
      }
    }
  }
}
.user-dropdown {
  .el-dropdown-menu__item {
    white-space: nowrap;
  }
}
</style>
