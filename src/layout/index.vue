<template>
  <div class="min-layout-content layout">
    <AppHeader />
    <AppMain class="app-main" />
    <AppFooter />
  </div>
</template>
<script>
import { AppHeader, AppMain, AppFooter } from "./components";
export default {
  name: "layout",
  components: {
    App<PERSON>ead<PERSON>,
    App<PERSON><PERSON>,
    AppFooter,
  },
};
</script>
<style lang="scss" scoped>
.layout {
  height: 100%;
  display: flex;
  flex-direction: column;
  .app-main {
    flex-grow: 1;
  }
}
</style>
