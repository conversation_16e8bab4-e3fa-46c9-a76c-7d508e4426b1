import Vue from "vue";
import VueRouter from "vue-router";
import HomeView from "@/views/HomeView/index.vue";
import Layout from "@/layout/index.vue";

Vue.use(VueRouter);

const routes = [
  {
    path: "/",
    component: Layout,
    redirect: "/home",
    children: [
      {
        path: "/home",
        name: "home",
        component: HomeView,
      },
      {
        path: "/theoreticalteaching",
        name: "theoreticalteaching",
        component: () =>
          import(
            /* webpackChunkName: "about" */ "@/views/TheoreticalTeaching.vue"
          ),
      },
    ],
  },
  {
    path: "/map",
    name: "map",
    component: () => import("@/views/MapView/index.vue"),
  },
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/login.vue"),
  },
  {
    path: "*",
    redirect: "/",
  },
];

const router = new VueRouter({
  routes,
});

export default router;
