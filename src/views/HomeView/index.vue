<template>
  <div class="home-view">
    <div class="ai-module">
      <div class="title">
        <img src="@/assets/images/AI.gif" alt="" />
        <h2>数字马院·智启思政未来</h2>
        <h4>智能搜索，精准直达</h4>
      </div>
      <AiModelView />
      <div class="flex-start-start other-links">
        <div
          class="flex-center-center link-item"
          v-for="item in links"
          :key="item.url"
          @click="goToSZMYAi(item.url)"
        >
          <svg-icon :icon-class="item.icon" /> {{ item.name }}
        </div>
      </div>
    </div>
    <div class="layout-content lljx-module">
      <ModuleTitle
        title="理论教学"
        discription="以思政教学智能体牵引理实一体化教学"
        @more="lljxMore"
      />
      <div class="content">
        <lljxView />
      </div>
    </div>
    <div class="sjjx-module" :id="$sjjxDocumentIdName">
      <div class="layout-content">
        <ModuleTitle
          title="实践教学"
          discription="用 “活” 红色资源，让思政教学从课堂延伸到红色现场"
          :isShowMore="false"
          @more="sjjxMore"
        />
        <div class="content">
          <sjjxView />
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { goToSZMYAi } from "@/utils/no_login_required_for_redirection.js";
import AiModelView from "./components/AiModelView.vue";
import ModuleTitle from "@/components/ModuleTitle.vue";
import lljxView from "./components/lljxView.vue";
import sjjxView from "./components/sjjxView.vue";
export default {
  name: "HomeView",
  components: {},
  data() {
    return {
      links: [
        // 热点推送：https://szmy.ai.ulearning.cn/single/#/news
        // 政策解读：https://szmy.ai.ulearning.cn/single/#/documentPolicy
        // 智能阅读：https://szmy.ai.ulearning.cn/single/#/documentRead
        // 生成教案：https://szmy.ai.ulearning.cn/single/#/teachPlan
        // 资源检索：https://szmy.ai.ulearning.cn/single/#/resourceRetrieval
        {
          name: "热点推送",
          url: "/single/#/news",
          icon: "rdts",
        },
        {
          name: "政策解读",
          url: "/single/#/documentPolicy",
          icon: "zcjd",
        },
        {
          name: "智能阅读",
          url: "/single/#/documentRead",
          icon: "znyd",
        },
        {
          name: "生成教案",
          url: "/single/#/teachPlan",
          icon: "scja",
        },
        {
          name: "资源检索",
          url: "/single/#/resourceRetrieval",
          icon: "zyjs",
        },
      ],
    };
  },
  components: {
    AiModelView,
    ModuleTitle,
    lljxView,
    sjjxView,
  },
  computed: {
    ...mapGetters("user", ["token"]),
  },
  mounted() {
    try {
      const aim = sessionStorage.getItem("SCROLL_TO_AIM_DOCUMENT");
      sessionStorage.removeItem("SCROLL_TO_AIM_DOCUMENT");
      if (aim) {
        document.getElementById(aim).scrollIntoView({
          behavior: "smooth", // 启用平滑滚动
          block: "start", // 对齐方式
        });
      }
    } catch {}
  },
  methods: {
    goToSZMYAi,
    lljxMore() {
      this.$router.push("/theoreticalteaching");
    },
    sjjxMore() {},
  },
};
</script>
<style lang="scss" scoped>
.home-view {
  .ai-module {
    padding-top: 62px;
    padding-bottom: 60px;
    max-width: 800px;
    margin: 0 auto;
    .title {
      width: fit-content;
      margin: 0 auto;
      margin-bottom: 25px;
      position: relative;
      img {
        width: 68px;
        height: 68px;
        position: absolute;
        top: -12px;
        left: -72px;
      }
      > h2 {
        font-weight: 600;
        font-size: 30px;
        line-height: 36px;
        margin-bottom: 9px;
      }
      > h4 {
        font-weight: 400;
        color: #475467;
        font-size: 18px;
        line-height: 28px;
        text-align: center;
      }
    }
    .other-links {
      margin-top: 24px;
      .link-item {
        height: 40px;
        min-height: 24px;
        max-height: 40px;
        padding: var(--Space-4, 4px) var(--Space-Large-16, 16px);
        border-radius: var(--el-border-radius-large);
        background: #f5f7fa;
        color: var(---el-text-color-primary);
        font-family: var(--el-font-family);
        font-size: var(--el-font-size-medium);
        font-style: normal;
        font-weight: 400;
        line-height: var(--el-font-line-height-medium); /* 150% */
        cursor: pointer;
        margin-right: 24px;
        .svg-icon {
          font-size: 18px;
          margin-right: 4px;
        }
        &:hover {
          background: #e6e8eb;
        }
      }
    }
  }
  .lljx-module {
    .content {
      padding-top: 32px;
    }
  }
  .sjjx-module {
    height: 866px;
    padding-top: 50px;
    background: url("@/assets/images/sjjx_bg.png") no-repeat;
    background-size: cover;
    .content {
      padding-top: 32px;
    }
  }
}
</style>
