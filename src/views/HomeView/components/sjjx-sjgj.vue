<template>
  <div class="sjjx-sjgj">
    <div class="flex-center-between content">
      <div class="image-box">
        <img src="@/assets/images/sjjx-face.png" />
      </div>
      <div class="info-box">
        <div class="title">实践教学管理系统</div>
        <div class="discription">
          信息化与AI技术赋能实践教学全流程管理，优化运行方式，提升成效，实现成果数字化，为持续改进提供数据支撑。
        </div>
        <div class="flex-start-start flex-warp features">
          <div class="flex-center feature">
            <svg-icon icon-class="feature1" /><span>混合式开放式全员实践</span>
          </div>
          <div class="flex-center feature">
            <svg-icon icon-class="feature2" /><span>全流程精细化数据管理</span>
          </div>
          <div class="flex-center feature">
            <svg-icon icon-class="feature3" /><span>AI智能助手多场景应用</span>
          </div>
          <div class="flex-center feature">
            <svg-icon icon-class="feature4" /><span>AI智能助手多场景应用</span>
          </div>
        </div>
        <div class="btn-box">
          <el-button type="primary" @click="goToSJJX()"
            >查看更多<i class="el-icon-right"></i
          ></el-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { goToSJJX } from "@/utils/no_login_required_for_redirection.js";
export default {
  name: "sjjx-sjgj",
  methods: {
    goToSJJX,
  },
};
</script>
<style lang="scss" scoped>
.sjjx-sjgj {
  .content {
    .image-box {
      width: 588px;
      height: 327px;
      padding: 17px 74px 40px 81px;
      background: url("@/assets/images/computer.png") no-repeat;
      background-size: 100% 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }
    .info-box {
      max-width: 476px;
      .title {
        color: var(---el-text-color-primary);
        font-family: "PingFang SC";
        font-size: 24px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
      }
      .discription {
        color: var(--el-text-color-secondary);
        font-family: "PingFang SC";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 24px; /* 150% */

        margin-top: 19px;
      }
      .features {
        margin-top: 42px;
        .feature {
          width: 50%;
          margin-bottom: 25px;
          color: var(---el-text-color-primary);
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 400;
          line-height: normal;

          .svg-icon {
            height: 26px;
            width: 26px;
            margin-right: 8px;
          }
        }
      }
      .btn-box {
        margin-top: 96px;
      }
    }
  }
}
</style>
