<template>
  <div class="sjjx-view">
    <div class="flex-center-center">
      <div class="flex-center-center control">
        <div
          class="flex-center-center control-item"
          :class="{ 'active-tab': activeTab === item.id }"
          v-for="item in tabs"
          :key="item.id"
          @click="changeActiveTab(item)"
        >
          <svg-icon :icon-class="item.icon" /> <span>{{ item.name }}</span>
        </div>
      </div>
    </div>

    <SjjxSjdt v-if="activeTab === 'sjjx-sjdt'" />
    <SjjxSjgj v-show="activeTab === 'sjjx-sjgj'" />
    <SjjxSjkc v-show="activeTab === 'sjjx-sjkc'" />
  </div>
</template>
<script>
import SjjxSjdt from "./sjjx-sjdt.vue";
import SjjxSjgj from "./sjjx-sjgj.vue";
import SjjxSjkc from "./sjjx-sjkc.vue";

export default {
  name: "sjjxView",
  components: {
    SjjxSjdt,
    SjjxSjgj,
    SjjxSjkc,
  },
  data() {
    return {
      activeTab: null,
      tabs: [
        {
          name: "数字地图",
          icon: "map-draw",
          id: "sjjx-sjdt",
        },
        {
          name: "实践工具",
          icon: "code-computer",
          id: "sjjx-sjgj",
        },
        {
          name: "实践课程",
          icon: "book-one",
          id: "sjjx-sjkc",
        },
      ],
    };
  },
  mounted() {
    try {
      this.activeTab = sessionStorage.getItem("activeTab") || this.tabs[0].id;
    } catch {
      this.activeTab = this.tabs[0].id;
    }
  },
  methods: {
    changeActiveTab(item) {
      this.activeTab = item.id;
      sessionStorage.setItem("activeTab", this.activeTab);
    },
  },
};
</script>
<style lang="scss" scoped>
.sjjx-view {
  .control {
    border-radius: 10px;
    border: 1px solid var(--el-border-color-lighter);
    background: var(--el-color-white);
    box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.04);
    padding: 4px;
    margin-bottom: 48px;
    .control-item {
      width: 156px;
      height: 48px;
      flex-shrink: 0;
      border-radius: 8px;
      background: #fff;

      color: var(---el-text-color-primary);

      /* medium/Headline1-Medium */
      font-family: var(--el-font-family);
      font-size: var(--el-font-size-extra-large);
      font-style: normal;
      font-weight: 500;

      cursor: pointer;
      margin-right: 24px;
      .svg-icon {
        margin-right: 8px;
        height: 20px;
        width: 20px;
      }
      &:last-child {
        margin-right: 0;
      }
      &:hover {
        color: var(--el-color-primary);
      }
    }
    .active-tab {
      background: var(--el-color-primary);
      color: #fff !important;
    }
  }
}
</style>
