<template>
  <div class="ai-model-view">
    <div class="flex-start-start upload-warpper" v-if="uploadImages.length > 0">
      <div
        class="image-content"
        v-for="(item, index) in uploadImages"
        :key="item.fileKey"
      >
        <div class="image-box">
          <el-image
            v-if="item.status === 2 || !item.status"
            :src="item.url"
            fit="cover"
          ></el-image>
        </div>

        <el-progress
          v-if="item.status === 1"
          class="float-center img-progress"
          type="circle"
          :percentage="item.percent"
          :color="'#ffffff'"
          :width="34"
          :stroke-width="2"
        >
        </el-progress>
        <i class="el-icon-error" @click="removeImage(item, index)"></i>
      </div>
      <div
        class="image-content image-content-upload-btn"
        @click="continueUpload"
      >
        <i class="float-center el-icon-plus"></i>
      </div>
    </div>
    <el-input
      type="textarea"
      :rows="5"
      placeholder="请输入你的问题和需求"
      v-model="textarea"
      @keyup.enter.native="openAI"
    >
    </el-input>
    <div class="flex-center-between control">
      <div class="left">
        <el-select
          v-model="model"
          placeholder="请选择"
          @change="changeModel"
          size="small"
          class="model-select"
          popper-class="model-select-popper"
        >
          <template #prefix v-if="model">
            <img
              class="icon"
              :src="modelIcon"
              alt=""
              style="height: 16px; width: 16px"
            />
          </template>
          <el-option
            v-for="item in showModelList"
            :key="item.modelId"
            :label="item.modelName"
            :value="item.modelId"
          >
            <img
              class="icon"
              :src="require(`@/assets/images/${item.icon}.png`)"
            />
            <span>{{ item.modelName }}</span>
          </el-option>
        </el-select>
        <el-button
          plain
          size="small"
          :class="{ 'internet-search-active': internetSearch }"
          v-if="showInternetSearch"
          @click="internetSearch = !internetSearch"
          ><svg-icon icon-class="internet" />联网搜索</el-button
        >
      </div>
      <div class="flex-center right">
        <div v-show="showUploadImage" class="upload-btn-wrapper">
          <el-button plain class="upload-btn" id="modelUploadImg" v-if="token"
            ><svg-icon icon-class="image-up"
          /></el-button>
          <template v-else>
            <el-tooltip
              class="item"
              effect="dark"
              content="请登录"
              placement="top"
            >
              <el-button plain class="upload-btn" disabled
                ><svg-icon icon-class="image-up"
              /></el-button>
            </el-tooltip>
          </template>
        </div>

        <img src="@/assets/images/send_btn.png" alt="" @click="openAI" />
      </div>
    </div>
    <el-drawer
      size="100%"
      destroy-on-close
      :visible.sync="drawer"
      append-to-body
      modal-append-to-body
      custom-class="ai-assistant-drawer"
    >
      <template #title>
        <div>
          <img src="@/assets/images/drawer-title.png" />
        </div>
      </template>
      <template>
        <iframe
          id="ai-assistant-iframe"
          :src="szAiSrc"
          frameborder="0"
          scrolling="no"
        />
      </template>
    </el-drawer>
  </div>
</template>
<script>
import { getPortalOrgInfo, getAIConfigInfo } from "@/api/index";
import { mapState, mapGetters, mapMutations } from "vuex";
import { obsUploader } from "@/utils/obsUpload.js";
import { handleNum, objectToQueryString } from "@/utils/index.js";
import { login } from "@/utils/login.js";
const ALL_MODEL_LIST = [
  { icon: "deepseek-icon", modelName: "DeepSeek", modelId: 3 },
  { icon: "qianwen-icon", modelName: "通义千问", modelId: 1 },
  { icon: "doubao-icon", modelName: "豆包", modelId: 2 },
];
const AI_MODEL_ENUM = {
  TONGYI: 1, // 通义千问
  TONGYI_VL: 4, // 通义千问VL
  DOUBAO: 2, // 豆包
  DEEPSEEK_R1: 3, // DeepSeekR1
  DEEPSEEK_ONLINE: 5, // DeepSeekR1-Online
};
const DOT_SHOW_MODELID_LIST = [
  AI_MODEL_ENUM.TONGYI_VL,
  AI_MODEL_ENUM.DEEPSEEK_ONLINE,
];
export default {
  name: "AiModelView",
  data() {
    return {
      textarea: "",
      showModelList: [],
      enableModelList: [],
      enableModelIds: [],
      model: null,
      modelIcon: "",
      imageModel: {
        maxSize: 0,
        maxNum: 8,
      },
      uploadImages: [],
      internetSearch: false,
      drawer: false,
      assistantId: null,
      szAiSrc: "",
    };
  },
  computed: {
    ...mapGetters("user", ["token"]),
    showInternetSearch() {
      const hasDeepSeekOnline = this.enableModelIds?.includes(
        AI_MODEL_ENUM.DEEPSEEK_ONLINE
      );
      return hasDeepSeekOnline && this.model === AI_MODEL_ENUM.DEEPSEEK_R1;
    },
    showUploadImage() {
      const hasTongYiVl = this.enableModelIds?.includes(
        AI_MODEL_ENUM.TONGYI_VL
      );
      return hasTongYiVl && this.model === AI_MODEL_ENUM.TONGYI;
    },
  },
  mounted() {
    this.init();
    if (this.token) {
      this.resUpload();
    }
  },
  methods: {
    async init() {
      let portalOrgid;
      try {
        let res = await getPortalOrgInfo({ domain: "zncd" });
        portalOrgid = res.data.result.orgid;
      } catch {}
      try {
        let res = await getAIConfigInfo({ orgId: portalOrgid });
        const { modelType, modelInfoList, id } =
          res.data.result.defaultAssistantModel;
        this.assistantId = id;
        this.enableModelList = modelInfoList.filter((item) => item.enable == 1);

        this.showModelList = [];
        this.enableModelIds = [];
        this.enableModelList.forEach((item) => {
          this.enableModelIds.push(item.modelId);
          if (!DOT_SHOW_MODELID_LIST.includes(item.modelId)) {
            this.showModelList.push(item);
          }
        });
        if (modelType) {
          this.model = modelType;
          if (this.model === AI_MODEL_ENUM.DEEPSEEK_ONLINE) {
            this.internetSearch = true;
            this.model = AI_MODEL_ENUM.DEEPSEEK_R1;
          }
          if (this.model === AI_MODEL_ENUM.TONGYI_VL) {
            this.model = AI_MODEL_ENUM.TONGYI;
          }
          this.changeModel(this.model);
        }
      } catch {}
    },
    changeModel(value) {
      this.modelIcon = require(`@/assets/images/${
        this.showModelList.find((item) => item.modelId == value).icon
      }.png`);
    },
    getUniqueValue() {
      const n = new Date().getTime() * (Math.random() * 100 + 1);
      return parseInt(n.toString());
    },
    resUpload() {
      const obs = obsUploader({
        multiple: true,
        maxSize: this.imageModel.maxSize,
        exts: "png,jpg,gif,jpeg,bmp",
      });
      obs.initUpBtn(document.getElementById("modelUploadImg"));
      obs.handleFileChange = (files) => {
        if (
          this.imageModel.maxNum &&
          this.uploadImages.length + files.length > this.imageModel.maxNum
        ) {
          this.$message({
            message: `一次仅可上传${this.imageModel.maxNum}张图片`,
            grouping: true,
            type: "warning",
          });
          return false;
        }
        obs.uploadFiles(files);
      };
      obs.onBeforeUpload = (file) => {
        let myFile = {
          fileKey: this.getUniqueValue(),
          percent: 0,
          url: "",
          status: 1,
          errMessage: "",
          cancel: () => {},
        };
        file.onProgress = (progress) => {
          myFile.percent = handleNum(progress, 2, "floor");
        };
        file.onSuccess = () => {
          myFile.url = file.obsHost + "/" + file.key;
          myFile.status = 2;
        };
        file.onError = () => {
          myFile.errMessage = "上传失败";
          myFile.status = 3;
        };
        myFile.cancel = () => {
          obs.cancelUpload(file);
        };
        // emit('fileAdd', myFile)
        this.uploadImages.push(myFile);
      };
      obs.onError = (file, err) => {
        if (err.code == 50002) {
          this.$message({
            message: `图片大小不能超过${this.imageModel.maxSize}M`,
            grouping: true,
            type: "warning",
          });
        }
        if (err.code == 50001) {
          this.$message({
            message: "不支持该类型文件",
            grouping: true,
            type: "warning",
          });
        }
      };
    },
    continueUpload() {
      if (this.uploadImages.length >= this.imageModel.maxNum) {
        return this.$message({
          message: `一次仅可上传${this.imageModel.maxNum}张图片`,
          grouping: true,
          type: "warning",
        });
      }
      document.getElementById("modelUploadImg").click();
    },
    removeImage(item, i) {
      if (item.status === 1 && item.cancel) {
        item.cancel();
      }
      let index = i;
      if (item.fileKey) {
        index = this.uploadImages.findIndex(
          (it) => it.fileKey === item.fileKey
        );
      }
      this.uploadImages.splice(index, 1);
    },
    openAI() {
      if (!this.token) {
        login();
        return;
      }
      if (this.enableModelIds.length === 0) {
        this.$message({
          message: "暂未配置默认助手，请联系管理员配置默认助手",
          grouping: true,
          type: "warning",
        });
        return;
      }
      if (!this.textarea) {
        this.$message({
          message: "输入内容不能为空",
          grouping: true,
          type: "warning",
        });
        return;
      }
      const auth = this.token || "";
      let final_model = this.model;
      if (this.model === AI_MODEL_ENUM.DEEPSEEK_R1 && this.internetSearch) {
        final_model = AI_MODEL_ENUM.DEEPSEEK_ONLINE;
      }
      if (this.model === AI_MODEL_ENUM.TONGYI && this.uploadImages.length > 0) {
        final_model = AI_MODEL_ENUM.TONGYI_VL;
      }
      const query = {
        theme: "sz",
        source: "szmy",
        auth,
        sessionId: "",
        showFirstHistory: true,
        isSingle: true,
        isTransparent: true,
        model: final_model,
      };
      this.szAiSrc = `${process.env.VUE_APP_AI_HOST}/${
        this.assistantId
      }?${objectToQueryString(query)}`;
      this.drawer = true;
      //
      window.addEventListener("message", (e) => {
        if (e.data === "dynamicKnowledge") {
          let images = [];
          this.uploadImages.forEach((item) => {
            images.push(item.url);
          });
          const iframe = document.getElementById("ai-assistant-iframe");
          iframe.contentWindow?.postMessage(
            {
              defaultQuestion: this.textarea,
              images,
            },
            "*"
          );
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.ai-model-view {
  padding: 12px 16px 16px 16px;
  border: 1px solid #cdd0d6;
  border-radius: 16px;
  background: #fff;
  box-shadow: 0 4px 12px 0 rgba(0, 0, 0, 0.12);

  .upload-warpper {
    .image-content {
      height: 52px;
      width: 52px;
      background-color: #0006;
      border-radius: 10px;
      border: 1px solid #dcdfe6;
      margin-right: 16px;
      margin-bottom: 8px;
      position: relative;
      .image-box {
        height: 100%;
        width: 100%;
        border-radius: 10px;
        overflow: hidden;
        .el-image {
          height: 100%;
          width: 100%;
        }
      }

      .float-center {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .el-icon-plus {
        font-size: 24px;
        color: #667085;
      }
      ::v-deep .img-progress {
        .el-progress__text {
          font-size: 12px !important;
          color: #fff !important;
        }
      }
      .el-icon-error {
        font-size: 20px;
        position: absolute;
        top: -10px;
        right: -10px;
        cursor: pointer;
      }
    }
    .image-content-upload-btn {
      background-color: #f2f4f7;
      cursor: pointer;
    }
  }

  ::v-deep .el-textarea {
    textarea {
      padding: 0px;
      border: none;
      outline: none;
      resize: none;
    }
  }

  .control {
    .left {
      ::v-deep .model-select {
        width: 134px;
        margin-right: 12px;
        .el-input__prefix {
          left: 12px;
          display: flex;
          align-items: center;
          .selected-prefix {
            height: 16px;
            width: 16px;
          }
        }
      }
      .internet-search-active {
        color: #e60012;
        border-color: #f8b3b8;
        background: #fde6e7;
      }
    }
    .right {
      .upload-btn-wrapper {
        height: 20px;
      }
      ::v-deep .upload-btn {
        height: 20px;
        padding: 0px;
        border: 0px;
        border-color: transparent;
        padding-right: 17px;
        position: relative;
        .svg-icon {
          font-size: 20px;
        }
        &::after {
          content: "";
          display: block;
          position: absolute;
          top: 50%;
          right: 0;
          transform: translateY(-50%);
          width: 1px;
          height: 16px;
          background-color: #d0d5dd;
        }
      }

      > img {
        height: 28px;
        width: 28px;
        cursor: pointer;
        margin-left: 16px;
      }
    }
  }
}
.model-select-popper {
  .el-select-dropdown__wrap {
    .el-select-dropdown__list {
      .el-select-dropdown__item {
        display: flex;
        align-items: center;
        gap: 4px;
        img {
          height: 16px;
          width: 16px;
        }
      }
    }
  }
}

::v-deep .ai-assistant-drawer {
  max-width: 1200px;
  .el-drawer__header {
    height: 48px;
    padding: 0px 20px;
    margin: 0px;
    align-items: center;
    img {
      height: 30px;
      width: 258px;
    }
  }
  iframe {
    width: 100%;
    height: calc(100vh - 48px);
    display: block;
  }
}
</style>
