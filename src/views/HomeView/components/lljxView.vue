<template>
  <div>
    <CardListLineThree
      :list="courseList"
      @myClick="(item) => openCourseDetail(item.id)"
    />
  </div>
</template>
<script>
import { MODEL_COURSE_LIST, openCourseDetail } from "@/utils/course.js";
import { getCoursesOverviewInfo, getCoursesDetailInfo } from "@/api/index.js";
import CardListLineThree from "@/components/CardListLineThree.vue";
export default {
  name: "lljxView",
  components: {
    CardListLineThree,
  },
  data() {
    return {
      courseList: MODEL_COURSE_LIST,
    };
  },
  created() {
    this.getCoursesOverInfo();
    this.getCoursesDetails();
  },
  methods: {
    openCourseDetail,
    getCoursesOverInfo() {
      MODEL_COURSE_LIST.forEach((item, index) => {
        getCoursesOverviewInfo({ textbookId: item.id })
          .then((res) => {
            const {
              pointNum,
              knowledgeRelationNum,
              ideologyNum,
              knowledgeWordNum,
            } = res.data;
            this.$set(this.courseList[index], "pointNum", pointNum);
            this.$set(
              this.courseList[index],
              "knowledgeRelationNum",
              knowledgeRelationNum
            );
            this.$set(this.courseList[index], "ideologyNum", ideologyNum);
            this.$set(
              this.courseList[index],
              "knowledgeWordNum",
              knowledgeWordNum
            );
          })
          .catch((err) => {
            console.warn(err);
          });
      });
    },
    getCoursesDetails() {
      MODEL_COURSE_LIST.forEach((item, index) => {
        getCoursesDetailInfo(item.id)
          .then((res) => {
            const { textbookTeacherList } = res.data.result;
            this.$set(
              this.courseList[index],
              "textbookTeacherList",
              textbookTeacherList
            );
          })
          .catch((err) => {
            console.warn(err);
          });
      });
    },
  },
};
</script>
