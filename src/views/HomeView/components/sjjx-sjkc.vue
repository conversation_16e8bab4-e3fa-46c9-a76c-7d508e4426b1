<template>
  <div class="sjjx-sjkc">
    <CardListLineFour :list="courseList" @myClick="openCourse" />
  </div>
</template>
<script>
import { getPremiumCourses } from "@/api/index.js";
import CardListLineFour from "@/components/CardListLineFour.vue";
export default {
  name: "sjjx-sjkc",
  components: {
    CardListLineFour,
  },
  data() {
    return {
      courseList: [],
    };
  },
  mounted() {
    this.getList();
  },
  methods: {
    getList() {
      getPremiumCourses()
        .then((res) => {
          this.courseList = res.data.courseList.slice(0, 8);
        })
        .catch((err) => {
          console.warn(err);
          this.courseList = [];
        });
    },
    openCourse(item) {
      window.open(
        `${process.env.VUE_APP_JPK_DETAIL_HOST}/homepage/index.html#/courseDetail/${item.id}`,
        "_blank"
      );
    },
  },
};
</script>
<!-- <style lang="scss" scoped>
.sjjx-sjkc {
}
</style> -->
