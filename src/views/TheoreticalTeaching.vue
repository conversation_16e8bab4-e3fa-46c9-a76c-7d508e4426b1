<template>
  <div class="theoretical-teaching">
    <div class="theoretical-teaching__bg">
      <div class="layout-content">
        <div class="title-warpper">
          <div class="title">思政课程</div>
          <div class="discription">共享优质思政教育资源 创新教学方法</div>
        </div>
        <CardListInLine
          :list="courseList"
          @myClick="(item) => openCourseDetail(item.id)"
        />
        <div class="page-nation">
          <el-pagination
            background
            layout="prev, pager, next"
            :page-size="pageSize"
            :current-page="pageNum"
            :total="total"
          >
          </el-pagination>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { MODEL_COURSE_LIST, openCourseDetail } from "@/utils/course.js";
import { getCoursesOverviewInfo, getCoursesDetailInfo } from "@/api/index.js";
import CardListInLine from "@/components/CardListInLine.vue";
export default {
  name: "TheoreticalTeaching",
  data() {
    return {
      courseList: MODEL_COURSE_LIST,
      pageSize: 10,
      pageNum: 1,
      total: 0,
    };
  },
  components: {
    CardListInLine,
  },
  async created() {
    this.getCoursesOverInfo();
    this.getCoursesDetails();
    this.total = this.courseList.length;
  },
  mounted() {
    this.$nextTick(() => {
      window.scrollTo({
        top: 0,
        left: 0,
        behavior: "smooth",
      });
    });
  },
  methods: {
    openCourseDetail,
    getCoursesOverInfo() {
      MODEL_COURSE_LIST.forEach((item, index) => {
        getCoursesOverviewInfo({ textbookId: item.id })
          .then((res) => {
            const {
              pointNum,
              knowledgeRelationNum,
              ideologyNum,
              knowledgeWordNum,
            } = res.data;
            this.$set(this.courseList[index], "pointNum", pointNum);
            this.$set(
              this.courseList[index],
              "knowledgeRelationNum",
              knowledgeRelationNum
            );
            this.$set(this.courseList[index], "ideologyNum", ideologyNum);
            this.$set(
              this.courseList[index],
              "knowledgeWordNum",
              knowledgeWordNum
            );
          })
          .catch((err) => {
            console.warn(err);
          });
      });
    },
    getCoursesDetails() {
      MODEL_COURSE_LIST.forEach((item, index) => {
        getCoursesDetailInfo(item.id)
          .then((res) => {
            const { description, textbookTeacherList } = res.data.result;
            this.$set(this.courseList[index], "description", description);
            this.$set(
              this.courseList[index],
              "textbookTeacherList",
              textbookTeacherList
            );
          })
          .catch((err) => {
            console.warn(err);
          });
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.theoretical-teaching {
  background: url("@/assets/images/theoretical-teaching-bg.png") no-repeat;
  background-size: 100% 453px;
  background-position: center top;
  padding-bottom: 77px;
  &__bg {
    background: url("@/assets/images/theoretical-teaching-bg-position.png")
      no-repeat;
    background-size: 100% 359px;
    background-position: center 94px;
    .title-warpper {
      padding-top: 48px;
      padding-bottom: 65px;
      text-align: center;
      .title {
        color: var(--el-text-color-write);
        font-family: "HarmonyOS Sans SC";
        font-size: 40px;
        font-style: normal;
        font-weight: 700;
        line-height: normal;
        letter-spacing: 4px;
        margin-bottom: 14px;
      }
      .discription {
        color: var(--el-text-color-write);
        font-family: "HarmonyOS Sans SC";
        font-size: 18px;
        font-style: normal;
        font-weight: 400;
        line-height: 20px; /* 111.111% */
        letter-spacing: 3.6px;
      }
    }
    .page-nation {
      text-align: center;
      margin-top: 40px;
    }
  }
}
</style>
