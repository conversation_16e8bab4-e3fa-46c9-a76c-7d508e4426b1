<template>
  <div class="login">
    <div class="login-title"><img src="@/assets/images/login-logo.png" /></div>
    <div class="login-main-warpper">
      <div class="login-info">
        <div class="title"><span class="font">账号登录</span></div>
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          class="login-ruleForm"
        >
          <el-form-item label="" prop="loginName">
            <el-input size="large" v-model="ruleForm.loginName"></el-input>
          </el-form-item>
          <el-form-item label="" prop="password">
            <el-input
              size="large"
              v-model="ruleForm.password"
              type="password"
            ></el-input>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              @click="submitForm('ruleForm')"
              class="submit-btn"
              >登录</el-button
            >
          </el-form-item>
        </el-form>
        <div class="register-password">
          <span @click="openUlearningRegister">忘记密码</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { mapGetters } from "vuex";
import { usersCheck, sz_login } from "@/api/index.js";
import { redirectByForm } from "@/utils/redirectByForm.js";
import cookies from "@/utils/cookie";
import { REDIRECT_LOGIN_URL } from "@/utils/config.js";
const {
  VUE_APP_IS_REDIRECT_LOGIN,
  VUE_APP_LOGIN_REDIRECT_URL,
  VUE_APP_ULEARNING_LOGIN_HOST,
} = process.env;

export default {
  name: "login",
  data() {
    return {
      ruleForm: {
        loginName: "",
        password: "",
      },
      rules: {
        loginName: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
    };
  },
  computed: {
    ...mapGetters("user", ["token"]),
  },
  created() {
    if (this.token) {
      this.$router.push("/");
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.check_account();
        } else {
          return false;
        }
      });
    },
    check_account() {
      usersCheck(this.ruleForm)
        .then((res) => {
          if (res.data.code != 1) {
            this.$message.error(res.data.message);
            return;
          }
          if (VUE_APP_IS_REDIRECT_LOGIN == "true") {
            this.submit();
          } else {
            sz_login(this.ruleForm)
              .then((res) => {
                cookies.set("USERINFO", JSON.stringify(res.data.result));
                cookies.set("USER_INFO", JSON.stringify(res.data.result));
                cookies.set(
                  "token",
                  JSON.stringify(res.data.result.authorization)
                );
                cookies.set(
                  "AUTHORIZATION",
                  JSON.stringify(res.data.result.authorization)
                );
                window.location.href = VUE_APP_LOGIN_REDIRECT_URL;
              })
              .catch((err) => {
                console.warn(err);
              });
          }
        })
        .catch((err) => {
          console.warn(err);
        });
    },
    submit() {
      const params = {
        ...this.ruleForm,
        loginRedirectUrl: VUE_APP_LOGIN_REDIRECT_URL,
      };
      redirectByForm(REDIRECT_LOGIN_URL, params);
    },
    openUlearningRegister() {
      const time = new Date().getTime();
      window.open(
        `${VUE_APP_ULEARNING_LOGIN_HOST}/?timestamp=${time}#/user/findPassword`,
        "_blank"
      );
    },
  },
};
</script>
<style lang="scss" scoped>
.login {
  width: 100%;
  height: 100%;
  background: url("@/assets/images/login-bg.png") no-repeat;
  background-size: cover;
  position: relative;
  .login-title {
    position: absolute;
    top: 26px;
    left: 21.875%;
    > img {
      width: 100%;
      max-width: 400px;
    }
  }
  .login-main-warpper {
    width: 46%;
    height: 100%;
    position: absolute;
    top: 0;
    right: 0;
    .login-info {
      width: 100vw;
      max-width: 460px;

      border-radius: 4px;
      background-color: #fff;
      box-shadow: 0 4px 20px 0 rgba(0, 0, 0, 0.05);
      padding: 60px 40px 40px 40px;
      overflow: hidden;

      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);

      .title {
        text-align: center;
        .font {
          color: #333;
          font-size: 20px;
          font-weight: 600;
          line-height: 28px;
          position: relative;
          &::after {
            content: "";
            display: block;
            height: 5px;
            width: 100%;
            background: linear-gradient(
              90deg,
              #ff8a65,
              rgba(255, 138, 101, 0) 100%
            );
            position: absolute;
            left: 0;
            bottom: 0;
          }
        }
      }

      ::v-deep .login-ruleForm {
        margin-top: 36px;
        .el-form-item {
          margin-bottom: 20px;
          .el-form-item__content {
            .el-input {
              .el-input__inner {
                height: 44px;
                line-height: 44px;
                border-color: #f0f2f5;
                padding: 0 16px;
              }
            }
            .submit-btn {
              height: 44px;
              width: 100%;
              background-color: #e64e3b;
              border-color: #e64e3b;
              margin-top: 69px;
            }
          }
        }
      }

      .register-password {
        margin-top: 15px;
        text-align: right;
        > span {
          color: #a8abb2;
          font-size: 14px;
          font-weight: 400;
          line-height: 20px;
          cursor: pointer;
        }
      }
    }
  }
  @media (max-width: 920px) {
    .login-title {
      left: 50%;
      transform: translateX(-50%);
    }
    .login-main-warpper {
      width: 100%;
      .login-info {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
    }
  }
}
</style>
