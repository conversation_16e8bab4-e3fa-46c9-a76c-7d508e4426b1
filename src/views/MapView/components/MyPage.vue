<template>
  <div v-if="list.length" class="page">
    <i
      class="el-icon-arrow-left"
      :class="{ disabled: prevDisabled }"
      @click="prev"
    ></i>
    <span class="active-page">{{ pageInfo.pn }}</span>
    <span>{{ pages }}</span>
    <i
      class="el-icon-arrow-right"
      :class="{ disabled: nextDisabled }"
      @click="next"
    ></i>
  </div>
</template>

<script>
export default {
  props: {
    currentList: {
      type: Array,
      default: () => [],
    },
    pageNum: {
      type: Number,
      default: 1,
    },
    pageSize: {
      type: Number,
      default: 10,
    },
    totalList: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      pageInfo: {
        pn: this.pageNum,
        ps: this.pageSize,
      },
      list: this.totalList,
    };
  },
  watch: {
    totalList(val) {
      this.list = val;
      this.pageInfo.pn = 1;
      this.getList();
    },
  },
  computed: {
    // 总页数
    pages() {
      return Math.ceil(this.list.length / this.pageInfo.ps) || 1;
    },
    // 上一页按钮禁用
    prevDisabled() {
      return this.pageInfo.pn <= 1;
    },
    // 下一页按钮禁用
    nextDisabled() {
      return this.pageInfo.pn >= this.pages;
    },
  },
  methods: {
    getList() {
      const start = (this.pageInfo.pn - 1) * this.pageInfo.ps;
      const end = this.pageInfo.pn * this.pageInfo.ps;
      const list = this.list.slice(start, end);
      this.$emit("update:currentList", list);
    },
    // 上一页
    prev() {
      if (this.prevDisabled) return;
      this.pageInfo.pn--;
      this.getList();
    },
    // 下一页
    next() {
      if (this.nextDisabled) return;
      this.pageInfo.pn++;
      this.getList();
    },
  },
};
</script>

<style lang="scss" scoped>
// @import "~@/styles/variables.scss";
.page {
  display: flex;
  align-items: center;
  gap: 2px;
  color: #a8abb2;
  i {
    font-size: 16px;
    cursor: pointer;
    &:hover {
      color: var(--color-primary);
    }
  }
  span {
    font-size: 14px;
  }
  .active-page {
    color: #475467;
    &::after {
      content: "/";
      margin-left: 2px;
      color: #a8abb2;
    }
  }
  .disabled {
    color: #d4d7de;
    cursor: not-allowed;
    &:hover {
      color: #d4d7de;
    }
  }
}
</style>
