<template>
  <div v-if="id" class="base-list">
    <div class="title">{{ provinceName }}</div>
    <div class="tags">
      <div
        class="tag"
        :class="{ 'is-active': activeTag === tag.id }"
        v-for="tag in tags"
        :key="tag.id"
        v-animate
        @click="tagClick(tag.id)"
      >
        {{ tag.typeName }}（{{ tag.count }}）
      </div>
    </div>
    <div class="title-block">
      <div class="title">基地列表</div>
      <MyPage
        :currentList.sync="currentBaseList"
        :page-num="pageInfo.pageNum"
        :page-size="pageInfo.pageSize"
        :totalList="baseList"
      />
    </div>
    <div class="list">
      <div
        class="base-item"
        v-for="base in currentBaseList"
        :key="base.id"
        v-animate
        @click="openBase(base)"
      >
        <img src="@/assets/images/base-icon.png" alt="" width="18" />
        <!-- <img src="@/assets/images/document.png" alt="" width="27"> -->
        <!-- <img src="@/assets/images/video.png" alt="" width="27"> -->
        <div class="name">{{ base.projectName }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { apiGetPracticeMap } from "@/api/map.js";
import MyPage from "./MyPage.vue";
export default {
  components: {
    MyPage,
  },
  data() {
    return {
      id: null,
      provinceName: "",
      tags: [],
      activeTag: 0,
      currentBaseList: [],
      pageInfo: {
        pageNum: 1, // 当前页码
        pageSize: 5, // 每页显示数量
      },
      baseList: [],
    };
  },
  methods: {
    async init(id, provinceName) {
      if (!id) return;
      if (this.id === id) return;
      this.id = id;
      this.provinceName = provinceName;
      this.tags = [];
      this.activeTag = 0;
      const res = await this.getBase();
      this.tags = res.typeInfos || [];
      this.activeTag = this.tags[0].id; // 默认选中第一个标签
    },
    async getBase() {
      try {
        const {
          data: { result: res },
        } = await apiGetPracticeMap({
          pid: this.id,
          type: this.activeTag,
        });
        this.baseList = res.teachMapDataDtoList;
        return res;
      } catch {
        return {};
      }
    },
    tagClick(id) {
      this.activeTag = id;
      this.getBase();
    },
    openBase(base) {
      // window.open(base.projectUrl)
    },
  },
};
</script>

<style lang="scss" scoped>
// @import "~@/styles/variables.scss";
.title {
  color: #182230;
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 10px;
}

// 主题
.tags {
  min-height: 76px;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-bottom: 40px;
  .tag {
    height: 32px;
    padding: 0 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 6px;
    border: 1px solid #ff5254;
    background: #ffeeec;
    color: #e60012;
    font-size: 14px;
    font-weight: 400;
    cursor: pointer;
    transition: background 0.5s;
    &.is-active {
      background: #e60012;
      color: #fff;
      font-weight: 600;
    }
  }
}

// 基地列表
.title-block {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.list {
  height: 210px;
  .base-item {
    width: 100%;
    height: 42px;
    padding: 10px 0;
    border-bottom: 1px dotted #fabc6b;
    display: flex;
    align-items: center;
    gap: 8px;
    // cursor: pointer;
    &:last-child {
      border-bottom: none;
    }
    // &:hover {
    //   .name {
    //     color: $--color-primary;
    //     text-decoration: underline;
    //   }
    // }
    .name {
      color: #182230;
      font-size: 14px;
      font-weight: 400;
      transition: all 0.3;
    }
  }
}

.fade-leave-active,
.fade-enter-active {
  transition: all 1s;
}
.fade-enter-from,
.fade-leave-to {
  transform: translateX(0);
  opacity: 0;
}
.fade-enter-to {
  transform: translateX(-10px);
}
</style>
