<!-- 实践地点打卡分布 -->
<template>
  <div class="map-container">
    <div
      class="grid-box"
      ref="mapBoxRef"
      :style="{ height: `${height}px` }"
      @mouseenter="stopAutoplayTooltip"
      @mouseleave="startAutoplayTooltip"
    >
      <!-- map -->
      <div class="map-block">
        <div class="map" id="myMap"></div>
        <div class="zoom-block">
          <i class="el-icon-plus" @click="zoomMap('zoomIn')"></i>
          <i class="el-icon-aim" @click="zoomMap('reset')"></i>
          <i class="el-icon-minus" @click="zoomMap('zoomOut')"></i>
        </div>
      </div>
      <BaseList ref="baseListRef" />
    </div>
    <!-- tip -->
    <div class="tip-block">* 备注：以上地图数据来自于百度地图和高德地图。</div>
  </div>
</template>

<script>
// 获取地图纹理图
const img = document.createElement("img");
img.src = require("@/assets/images/map.png");
import { apiGetPracticeProvince, getChinaMap } from "@/api/map.js";
import * as echarts from "echarts";
import BaseList from "./components/BaseList.vue";
const mapOption = {
  legend: {
    show: true,
    icon: "circle",
    name: "所在地点",
    left: "10%",
    bottom: "0",
    width: "12",
    itemStyle: {
      color: "#FCD39D",
      borderColor: "#F79009",
      borderWidth: 1,
    },
    textStyle: {
      padding: [0, 0, 0, -5],
      fontSize: 14,
      lineHeight: 22,
      color: "#475467",
    },
    selectedMode: false,
  },
  geo: [
    {
      map: "china",
      aspectScale: 0.75,
      zoom: 1,
      layoutCenter: ["50%", "50%"],
      layoutSize: "110%",
      show: true,
      roam: true,
      animationDurationUpdate: 0, //实现缩放、拖动同步且不卡顿
      label: {
        show: true, // 各个省市县的名字
        color: "#475467",
        fontWeight: 400,
        fontSize: 10,
      },
      itemStyle: {
        areaColor: {
          image: img.src,
          repeat: "no-repeat",
        },
        borderColor: "#FF5254",
        borderWidth: 1,
      },
      emphasis: {
        itemStyle: {
          show: false,
          color: "#fff",
          areaColor: "#FCD39D",
          borderColor: "#F79009",
        },
        label: {
          show: true,
          color: "#AD6800",
        },
      },
    },
    // 重影
    {
      type: "map",
      map: "china",
      zlevel: -1,
      aspectScale: 0.75,
      zoom: 1,
      layoutCenter: ["50%", "51%"],
      layoutSize: "110%",
      roam: false,
      animationDurationUpdate: 0,
      silent: true,
      regions: [
        //隐藏南海诸岛阴影
        {
          name: "南海诸岛",
          itemStyle: {
            opacity: 0,
          },
          label: {
            show: false,
          },
        },
      ],
      itemStyle: {
        borderWidth: 1,
        borderColor: "#ff8686",
        areaColor: "#ff8686",
      },
    },
    {
      type: "map",
      map: "china",
      zlevel: -2,
      aspectScale: 0.75,
      zoom: 1,
      layoutCenter: ["50%", "52.5%"],
      layoutSize: "110%",
      roam: false,
      animationDurationUpdate: 0,
      silent: true,
      regions: [
        //隐藏南海诸岛阴影
        {
          name: "南海诸岛",
          itemStyle: {
            opacity: 0,
          },
          label: {
            show: false,
          },
        },
      ],
      itemStyle: {
        borderWidth: 1,
        borderColor: "#ffbbc3",
        shadowColor: "rgba(255, 206, 206, 0.4)",
        shadowOffsetY: 7,
        shadowBlur: 3,
        areaColor: "#ffbbc3",
      },
    },
  ],
  tooltip: {
    trigger: "item",
    position: function (point) {
      return [point[0], point[1] - 66];
    },
    borderWidth: 0,
    padding: 0,
    backgroundColor: "transparent",
    extraCssText: "box-shadow: none;",
    formatter: function (params) {
      let data = params.data;
      if (data.count) {
        return `<div class='custom-tooltip-style'>
          <span class="name">${data.name}</span>
          <span class="count">基地数量：${data.count}个</span>
        </div>`;
      }
    },
  },
  series: [
    {
      type: "map",
      map: "china",
      geoIndex: 0,
      data: [],
      select: {
        disabled: true, //不允许选中
      },
    },
    {
      name: "所在地点",
      type: "effectScatter",
      coordinateSystem: "geo",
      data: [],
      silent: true,
      symbol: "circle",
      symbolSize: function (value, params) {
        return params.data.symbolSize;
      }, //点位大小
      itemStyle: {
        //中心点属性
        color: "#FCD39D",
        shadowBlur: 10,
        shadowColor: "#FCD39D",
        opacity: 0.8,
      },
      effectType: "ripple",
      showEffectOn: "render", //emphasis移入显示动画，render一开始显示动画
      rippleEffect: {
        //涟漪属性
        color: "#F79009",
        scale: 5,
        brushType: "stroke",
      },
    },
  ],
};
export default {
  components: {
    BaseList,
  },
  data() {
    return {
      height: 500,
      mapEcharts: null,
      list: [], //高亮的地区列表
      timer: null,
      timer2: null,
      highlightIndex: 0, // 当前高亮的地区索引
    };
  },
  mounted() {
    // 切换浏览器tab栏或最小化浏览器时，停止循环tooltip
    document.addEventListener("visibilitychange", () => {
      if (document.hidden) {
        this.stopAutoplayTooltip();
      } else {
        this.startAutoplayTooltip();
      }
    });
    // this.getHeight();
    window.addEventListener("resize", this.resizeMap);
    this.getAreaList();
    this.$nextTick(()=>{
      this.getHeight();
    });
  },
  beforeDestroy() {
    this.stopAutoplayTooltip();
    window.removeEventListener("resize", this.resizeMap);
  },
  computed: {
    itemIndexList() {
      return Array.from({ length: this.list.length }, (_, i) => i);
    },
  },
  methods: {
    // 获取地点列表
    async getAreaList() {
      try {
        const {
          data: { result = [] },
        } = await apiGetPracticeProvince();
        this.list = result.sort((a, b) => b.count - a.count);
        await this.initMapDatas();
        this.initEcharts();
      } catch {}
    },
    // 加载中国地图
    async initMapDatas() {
      const name = "china";
      const data = await getChinaMap();
      // 过滤掉海南省其他部分的json之后的中国地图json
      const geoJSON = await this.formatJson(data.data);
      echarts.registerMap(name, { geoJSON });
      this.list = this.formatCenter(geoJSON);
      mapOption.geo.forEach((v) => {
        v.map = name;
      });
      mapOption.series[0].data = this.list;
      mapOption.series[1].data = this.list;
      this.mapEcharts = echarts.init(document.getElementById("myMap"));
      this.startAutoplayTooltip();
    },
    // 绘制地图
    initEcharts() {
      this.mapEcharts.setOption(mapOption);
      this.mapEcharts.on("georoam", (params) => this.onGeoRoam(params));
      this.mapEcharts.on("mouseover", this.onMouseover);
      this.mapEcharts.on("mouseout", this.onMouseout);
    },
    // 南沙诸岛以缩略图展示
    async formatJson(chinaGeoJson) {
      chinaGeoJson.features.forEach((v) => {
        if (v.properties && v.properties.name == "海南省") {
          v.geometry.coordinates = v.geometry.coordinates.slice(0, 1);
        }
      });
      // 过滤掉海南诸岛边界线
      chinaGeoJson.features = chinaGeoJson.features.filter(
        (item) => item.properties.adcode !== "100000_JD"
      );
      return chinaGeoJson;
    },
    // 获取地区中心点(散点位置)
    formatCenter(geoJson) {
      const newList = this.list.map((item, index) => {
        geoJson.features.forEach((cell) => {
          if (cell.properties.name.includes(item.provinceName)) {
            let coordinates;
            // 判断是否是多块的地图
            if (cell.geometry.type === "MultiPolygon") {
              coordinates = cell.geometry.coordinates[0][0];
            } else {
              coordinates = cell.geometry.coordinates[0];
            }
            //计算中心点
            let x = 0,
              y = 0,
              count = 0;
            for (let i = 0; i < coordinates.length; i++) {
              x += coordinates[i][0];
              y += coordinates[i][1];
              count++;
            }
            item.name = cell.properties.name;
            item.value = [x / count, y / count];
          }
        });
        // 散点大小
        item.symbolSize = (this.list.length - index + 5) * 0.5;
        return item;
      });
      return newList;
    },
    // 放大缩小以及拖拽时，让重影跟着动
    onGeoRoam(params) {
      let option = this.mapEcharts.getOption(); //获得option对象
      let len = option.geo.length;
      if (params.zoom) {
        //捕捉到缩放时
        for (var i = 0; i < len; i++) {
          mapOption.geo[i].center = option.geo[0].center;
          mapOption.geo[i].zoom = option.geo[0].zoom;
        }
        const fontSize = 10 * option.geo[0].zoom;
        mapOption.geo[0].label.fontSize = fontSize;
      } else {
        //捕捉到拖曳时
        for (var i = 0; i < len; i++) {
          mapOption.geo[i].center = option.geo[0].center;
        }
      }
      this.mapEcharts.setOption(mapOption); //设置option
    },
    // 循环轮播tooltip
    startAutoplayTooltip() {
      if (!this.mapEcharts) return;
      if (!this.list.length) return;
      this.stopAutoplayTooltip();
      const run = () => {
        const id = this.list[this.highlightIndex].id;
        const provinceName = this.list[this.highlightIndex].name;
        this.$refs.baseListRef?.init(id, provinceName);
        this.showTooltip(this.highlightIndex);
        this.highlightIndex++;
      };
      const loopEvent = () => {
        run();
        if (this.highlightIndex >= this.list.length) {
          this.highlightIndex = 0;
          this.startAutoplayTooltip();
        }
      };
      this.timer = setInterval(() => {
        loopEvent();
      }, 6000);
      setTimeout(()=>{
        loopEvent();
      });
    },
    // 高亮某个地图区域
    showTooltip(index) {
      this.mapEcharts.dispatchAction({
        type: "downplay",
        seriesIndex: 0,
        dataIndex: this.itemIndexList,
      });
      this.mapEcharts.dispatchAction({
        type: "showTip",
        seriesIndex: 0,
        dataIndex: index,
      });
      this.mapEcharts.dispatchAction({
        type: "highlight",
        seriesIndex: 0,
        dataIndex: index,
      });
    },
    // 停止循环轮播tooltip
    stopAutoplayTooltip() {
      clearInterval(this.timer);
      this.timer = null;
    },
    // 获取容器高度
    getHeight() {
      const width = this.$refs.mapBoxRef.offsetWidth;
      this.height = width * 0.4;
    },
    // 地图大小自适应
    resizeMap() {
      this.getHeight();
      if (this.mapEcharts) {
        this.mapEcharts.resize();
      }
    },
    // 放大/缩小/还原
    zoomMap(action) {
      var currentZoom = this.mapEcharts.getOption().geo[0].zoom;
      if (action === "zoomIn") {
        this.mapEcharts.setOption({
          geo: {
            zoom: currentZoom * 1.1,
          },
        });
      } else if (action === "zoomOut") {
        this.mapEcharts.setOption({
          geo: {
            zoom: currentZoom * 0.9,
          },
        });
      } else if (action === "reset") {
        this.mapEcharts.setOption({
          geo: {
            zoom: 1,
            center: undefined,
          },
        });
      }
      const params = { zoom: 1 };
      this.onGeoRoam(params);
    },
    // 鼠标进入地图
    onMouseover(params) {
      this.onMouseout();
      const { id, name } = params.data;
      if (!id) return;
      this.timer2 = setTimeout(() => {
        this.showTooltip(params.dataIndex);
        this.$refs.baseListRef.init(id, name);
      }, 1000);
    },
    // 鼠标离开地图
    onMouseout() {
      clearInterval(this.timer2);
      this.timer2 = null;
    },
  },
};
</script>

<style lang="scss" scoped>
.map-container {
  width: 100%;
  position: relative;
  .grid-box {
    width: 100%;
    height: 500px;
    display: grid;
    grid-template-columns: 3fr 2fr;
    align-items: center;
    gap: 80px;
    margin-bottom: 20px;
  }
  .map-block {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: flex-end;
    gap: 10px;
    .map {
      width: 100%;
      height: 100%;
    }
    .zoom-block {
      flex-shrink: 0;
      margin-bottom: 10%;
      width: 36px;
      height: 100px;
      border-radius: 4px;
      background: #fff;
      box-shadow: 0px 0px 12px 0px rgba(0, 0, 0, 0.12);
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 12px;
      i {
        font-size: 20px;
        color: #606266;
        cursor: pointer;
      }
    }
  }
  .tip-block {
    color: #98a2b3;
    font-size: 14px;
    font-weight: 400;
    line-height: 20px;
  }
}
// tooltip样式
::v-deep(.custom-tooltip-style) {
  width: 131px;
  min-height: 66px;
  background: url("@/assets/images/tooltip.webp") no-repeat center center;
  background-size: 100% 100%;
  color: #fff;
  display: flex;
  flex-direction: column;
  padding-top: 7px;
  padding-left: 22px;
  .name {
    color: #bf0000;
    font-size: 14px;
    font-weight: 500;
  }
  .count {
    color: #101824;
    font-size: 12px;
    font-weight: 400;
  }
}
</style>
