<template>
  <div class="module-title">
    <div class="title">
      <div class="left"></div>
      <span class="text">{{ title }}</span>

      <div class="right"></div>
    </div>
    <div class="line">
      <div class="line-normal"></div>
      <div class="line-weight"></div>
    </div>
    <div class="discription">
      {{ discription }}
    </div>
    <div class="more" @click="$emit('more')" v-if="isShowMore">
      查看更多<i class="el-icon-arrow-right"></i>
    </div>
  </div>
</template>
<script>
export default {
  name: "ModuleTitle",
  props: {
    title: {
      type: String,
      default: "",
      require: true,
    },
    discription: {
      type: String,
      default: "",
    },
    isShowMore: {
      type: Boolean,
      default: true,
    },
  },
};
</script>
<style lang="scss" scoped>
.module-title {
  position: relative;
  .title {
    display: flex;
    align-items: flex-end;
    .left {
      height: 42px;
      flex-grow: 1;
      background: url("@/assets/images/title_left.png") no-repeat;
      background-size: 100% 42px;
    }
    .text {
      text-align: center;
      font-family: "Source Han Serif CN";
      font-size: 38px;
      font-style: normal;
      font-weight: 900;
      line-height: 55px;
      letter-spacing: 2.28px;
      background: linear-gradient(180deg, #e60012 20.29%, #bf0000 86.96%);
      background-clip: text;
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      padding: 0 30px;
    }
    .right {
      height: 42px;
      flex-grow: 1;
      background: url("@/assets/images/title_right.png") no-repeat;
      background-size: 100% 42px;
    }
  }
  .line {
    position: relative;
    padding: 1px 0;
    margin-top: 3px;
    .line-weight {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 190px;
      height: 3px;
      background: #e60012;
    }
    .line-normal {
      width: 527px;
      height: 1px;
      background: #e00e0c;
      margin: 0 auto;
    }
  }
  .discription {
    color: var(--el-text-color-placeholder);
    text-align: center;
    font-family: "PingFang SC";
    font-size: 16px;
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    letter-spacing: 0.8px;
    margin-top: 7px;
  }
  .more {
    color: var(--el-text-color-placeholder);
    text-align: center;
    font-family: var(--el-font-family);
    font-size: var(--el-font-size-base);
    font-style: normal;
    font-weight: 400;
    line-height: var(--el-font-line-height-base); /* 142.857% */
    position: absolute;
    right: 0px;
    top: 46px;
    cursor: pointer;
  }
}
</style>
