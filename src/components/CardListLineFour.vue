<template>
  <div class="card-list-line-four">
    <div class="warpper" v-for="item in list" :key="item.id">
      <div class="card" @click="$emit('myClick', item)">
        <div class="image-box">
          <ImageView :src="item.cover" class="image" />
        </div>
        <div class="info-box">
          <div class="title">{{ item.name }}</div>
          <div class="flex-center other">
            <div class="source">{{ item.sourceSchool || "--" }}</div>
            <div class="btn" @click.stop="$emit('myClick', item)">立即查看</div>
          </div>
        </div>
      </div>
      <slot name="float">
        <div class="float-view">{{ item.viewCount }}人浏览过</div>
      </slot>
    </div>
  </div>
</template>
<script>
import ImageView from "@/components/ImageView.vue";
export default {
  name: "CardListLineFour",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    ImageView,
  },
};
</script>
<style lang="scss" scoped>
.card-list-line-four {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: flex-start;
  .warpper {
    width: calc(25% - 22.5px);
    margin-bottom: 30px;
    border-radius: 8px;
    position: relative;
    .card {
      width: 100%;
      border-radius: 8px;
      border: 1px solid var(--el-border-color-lighter);
      background: #fff;
      overflow: hidden;
      .image-box {
        width: 100%;
        padding-bottom: 56.25%;
        position: relative;
        overflow: hidden;
        cursor: pointer;
        .image {
          height: 100%;
          width: 100%;
          position: absolute;
          top: 0;
          left: 0;
          transition: all 0.5s;
        }
      }
      .info-box {
        padding: 10px 15px;
        .title {
          @include text-ellipsis;
          color: #333;
          font-family: "PingFang SC";
          font-size: 16px;
          font-style: normal;
          font-weight: 600;
          line-height: normal;
        }
        .other {
          margin-top: 5px;
          .source {
            flex: 1;
            @include text-ellipsis;
            color: #666;
            font-family: "PingFang SC";
            font-size: 14px;
            font-style: normal;
            font-weight: 400;
            line-height: normal;
          }
          .btn {
            cursor: pointer;
            margin-left: 24px;
            border-radius: 18px;
            opacity: 0.8;
            background: linear-gradient(278deg, #e5c27e -3.56%, #f0d8b3 100%);
            padding: 6px 13px 5px 13px;
            color: #5f4630;
            font-family: "PingFang SC";
            font-size: 12px;
            font-style: normal;
            font-weight: 500;
            line-height: normal;
          }
        }
      }
    }
    .float-view {
      position: absolute;
      top: -4px;
      right: 0px;
      background: url("@/assets/images/course-float.png") no-repeat;
      background-size: 100% 100%;
      width: 102px;
      height: 20px;
      flex-shrink: 0;
      color: #fff;
      text-align: right;
      font-family: "PingFang SC";
      font-size: 12px;
      font-style: normal;
      font-weight: 400;
      line-height: 20px;
      padding-right: 2px;
    }
    &:hover {
      .card {
        .image-box {
          .image {
            transform: scale(1.05);
          }
        }
        .info-box {
          .title {
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
}
</style>
