<template>
  <div class="card-list-line-three">
    <div class="warpper" v-for="item in list" :key="item.id">
      <div class="card" @click="$emit('myClick', item)">
        <div class="image-box">
          <ImageView :src="item.cover" class="image" />
        </div>
        <slot nane="content">
          <div class="content">
            <div class="title">{{ item.name }}</div>
            <div class="counts">
              <div class="count">
                <div class="count_text">知识点</div>
                <div class="count_num">{{ formatNumber(item.pointNum) }}</div>
              </div>
              <div class="count">
                <div class="count_text">知识点关系</div>
                <div class="count_num">
                  {{ formatNumber(item.knowledgeRelationNum) }}
                </div>
              </div>
              <div class="count">
                <div class="count_text">思政点</div>
                <div class="count_num">
                  {{ formatNumber(item.ideologyNum) }}
                </div>
              </div>
              <div class="count">
                <div class="count_text">知识字数</div>
                <div class="count_num">
                  {{ formatNumber(item.knowledgeWordNum) }}
                </div>
              </div>
            </div>
            <div class="flex-center author">
              <svg-icon icon-class="author" class="default-icon" />
              <span class="inline-font">{{
                getAuthorName(item.textbookTeacherList)
              }}</span>
            </div>
          </div>
        </slot>
      </div>
      <slot name="float"></slot>
    </div>
  </div>
</template>
<script>
import { formatNumber, getAuthorName } from "@/utils/index.js";
import ImageView from "@/components/ImageView.vue";
export default {
  name: "CardListLineThree",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    ImageView,
  },
  methods: {
    formatNumber,
    getAuthorName,
  },
};
</script>
<style lang="scss" scoped>
.card-list-line-three {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
  align-items: flex-start;
  .warpper {
    width: calc(100% / 3 - 20px);
    margin-bottom: 30px;
    position: relative;
    background-color: #fff;
    cursor: pointer;
    .card {
      width: 100%;
      border-radius: 12px;
      border: 1px solid var(--el-border-color-lighter);
      background: #fff;
      box-shadow: 0 12px 32px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      .image-box {
        width: 100%;
        padding-bottom: 56.25%;
        position: relative;
        overflow: hidden;
        cursor: pointer;
        .image {
          height: 100%;
          width: 100%;
          position: absolute;
          top: 0;
          left: 0;
          transition: all 0.5s;
        }
      }
      .content {
        padding: 16px 20px;
        .title {
          color: var(---el-text-color-primary);
          font-family: var(--el-font-family);
          font-size: var(--el-font-size-medium);
          font-style: normal;
          font-weight: 600;
          line-height: var(--el-font-line-height-medium);
          @include text-ellipsis;
        }
        .counts {
          border-radius: 8px;
          background: var(--el-fill-color-light);
          padding-top: 10px;
          padding-bottom: 8px;
          display: flex;
          justify-content: space-around;
          align-items: center;
          margin-top: 8px;
          .count {
            .count_text {
              color: var(--el-text-color-secondary);
              text-align: center;

              /* regular/body3-Regular */
              font-family: var(--el-font-family);
              font-size: var(--el-font-size-extra-small);
              font-style: normal;
              font-weight: 400;
              line-height: var(--el-font-line-height-extra-small); /* 150% */
            }
            .count_num {
              color: var(---el-text-color-primary);
              text-align: center;
              /* bold/Headline3-Bold */
              font-family: var(--el-font-family);
              font-size: var(--el-font-size-medium);
              font-style: normal;
              font-weight: 600;
              line-height: var(--el-font-line-height-medium); /* 150% */
            }
          }
        }
        .author {
          margin-top: 10px;
          .default-icon {
            margin-right: 4px;
            height: 14px;
            width: 14px;
          }
          .inline-font {
            display: inline-block;
            flex: 1;
            color: var(--el-text-color-secondary);
            font-family: "PingFang SC";
            font-size: 12px;
            font-weight: 400;
            line-height: 17px;
            @include text-ellipsis;
          }
        }
      }
    }
    &:hover {
      .card {
        .image-box {
          .image {
            transform: scale(1.05);
          }
        }
        .content {
          .title {
            color: var(--el-color-primary);
          }
        }
      }
    }
  }
}
</style>
