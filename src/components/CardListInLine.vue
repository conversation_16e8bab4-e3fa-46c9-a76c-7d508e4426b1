<template>
  <div class="card-list-in-line">
    <div class="warpper" v-for="item in list" :key="item.id">
      <div class="flex-start-start card" @click="$emit('myClick', item)">
        <div class="image-box">
          <ImageView :src="item.cover" class="image" />
          <div class="flex-center-center mask">
            <el-button
              type="primary"
              size="small"
              @click.stop="$emit('myClick', item)"
              >点击查看</el-button
            >
          </div>
        </div>
        <div class="info-box">
          <div class="title-author">
            <div class="title-width">
              <div class="title">{{ item.name }}</div>
            </div>

            <div class="flex-center author">
              <svg-icon icon-class="author" class="default-icon" />
              <span class="inline-font">{{
                getAuthorName(item.textbookTeacherList)
              }}</span>
            </div>
          </div>
          <div class="discription">
            <div v-html="item.description"></div>
          </div>
          <div class="flex-center flex-warp counts-btn">
            <div class="counts-wodth">
              <div class="counts">
                <div class="count">
                  <div class="count_text">知识点</div>
                  <div class="count_num">{{ formatNumber(item.pointNum) }}</div>
                </div>
                <div class="count">
                  <div class="count_text">知识点关系</div>
                  <div class="count_num">
                    {{ formatNumber(item.knowledgeRelationNum) }}
                  </div>
                </div>
                <div class="count">
                  <div class="count_text">思政点</div>
                  <div class="count_num">
                    {{ formatNumber(item.ideologyNum) }}
                  </div>
                </div>
                <div class="count">
                  <div class="count_text">知识字数</div>
                  <div class="count_num">
                    {{ formatNumber(item.knowledgeWordNum) }}
                  </div>
                </div>
              </div>
            </div>
            <el-button
              type="primary"
              size="small"
              @click.stop="$emit('myClick', item)"
              class="detail-btn"
              >点击查看</el-button
            >
          </div>
        </div>
      </div>
      <slot name="float"></slot>
    </div>
  </div>
</template>
<script>
import { formatNumber, getAuthorName } from "@/utils/index.js";
import ImageView from "@/components/ImageView.vue";
export default {
  name: "CardListInLine",
  props: {
    list: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {};
  },
  components: {
    ImageView,
  },
  methods: {
    formatNumber,
    getAuthorName,
  },
};
</script>
<style lang="scss" scoped>
.card-list-in-line {
  .warpper {
    background-color: #fff;
    margin-bottom: 40px;
    cursor: pointer;
    border-radius: 12px;
    .card {
      border-radius: 12px;
      border: 1px solid var(--el-border-color-lighter);
      background: #fff;
      box-shadow: 0 12px 32px 0 rgba(0, 0, 0, 0.05);
      overflow: hidden;
      .image-box {
        width: 380px;
        height: 215px;
        position: relative;
        overflow: hidden;
        .image {
          height: 100%;
          width: 100%;
          position: absolute;
          top: 0;
          left: 0;
          transition: all 0.5s;
        }
        .mask {
          position: absolute;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: rgba(0, 0, 0, 0.2);
          display: none;
        }
        &:hover {
          .image {
            transform: scale(1.1);
          }
          .mask {
            display: flex;
          }
        }
      }
      .info-box {
        width: calc(100% - 380px);
        padding: 20px 20px 20px 30px;
        .title-author {
          .title-width {
            .title {
              color: var(---el-text-color-primary);
              font-family: var(--el-font-family);
              font-size: var(--el-font-size-medium);
              font-style: normal;
              font-weight: 600;
              line-height: var(--el-font-line-height-medium);
              @include text-ellipsis;
            }
          }

          .author {
            margin-top: 3px;
            .default-icon {
              margin-right: 4px;
              height: 14px;
              width: 14px;
              min-width: 14px;
            }
            .inline-font {
              flex: 1;
              color: var(--el-text-color-secondary);
              font-family: "PingFang SC";
              font-size: 12px;
              font-weight: 400;
              line-height: 17px;
              white-space: nowrap;
              @include text-ellipsis;
            }
          }
        }
        .counts-btn {
          margin-top: 11px;
          .counts-wodth {
            flex: 1;
            min-width: 300px;
            .counts {
              width: 379px;
              border-radius: 8px;
              background: var(--el-fill-color-light);
              padding-top: 10px;
              padding-bottom: 8px;
              display: flex;
              justify-content: space-around;
              align-items: center;
              .count {
                .count_text {
                  color: var(--el-text-color-secondary);
                  text-align: center;

                  /* regular/body3-Regular */
                  font-family: var(--el-font-family);
                  font-size: var(--el-font-size-extra-small);
                  font-style: normal;
                  font-weight: 400;
                  line-height: var(
                    --el-font-line-height-extra-small
                  ); /* 150% */
                }
                .count_num {
                  color: var(---el-text-color-primary);
                  text-align: center;
                  /* bold/Headline3-Bold */
                  font-family: var(--el-font-family);
                  font-size: var(--el-font-size-medium);
                  font-style: normal;
                  font-weight: 600;
                  line-height: var(--el-font-line-height-medium); /* 150% */
                }
              }
            }
          }
          .detail-btn {
            display: none;
          }
        }
        .discription {
          overflow: hidden;
          color: var(--el-text-color-regular);
          font-family: var(--el-font-family);
          font-size: var(--el-font-size-base);
          font-style: normal;
          font-weight: 400;
          line-height: var(--el-font-line-height-base); /* 142.857% */
          @include text-ellipsis(2);
          margin-top: 12px;
        }
        &:hover {
          .counts-btn {
            .detail-btn {
              display: block;
            }
          }
        }
      }
    }
  }
}
</style>
