<template>
  <div class="image-view">
    <el-image :src="src" :fit="fit">
      <div slot="error" class="image-slot">
        <img src="@/assets/images/default.png" />
      </div>
    </el-image>
  </div>
</template>
<script>
export default {
  props: {
    src: {
      type: String,
      default: "",
    },
    fit: {
      type: String,
      default: "cover",
    },
  },
  name: "ImageView",
};
</script>
<style lang="scss" scoped>
.image-view {
  width: 100%;
  height: 100%;
  .el-image {
    width: 100%;
    height: 100%;
    .image-slot {
      width: 100%;
      height: 100%;
      img {
        width: 100%;
        height: 100%;
      }
    }
  }
}
</style>
