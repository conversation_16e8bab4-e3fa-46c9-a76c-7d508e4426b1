//保留n位小数
export function handleNum(num, precision = 1, method = "round") {
  const powers = Math.pow(10, precision);
  return /\./g.test(num + "") ? Math[method](num * powers) / powers : num;
}

/**
 * 将对象转为url查询字符串
 * @param params
 * @returns
 */
export function objectToQueryString(params) {
  return Object.keys(params)
    .filter(
      (key) =>
        params[key] !== null && params[key] !== undefined && params[key] !== ""
    ) // 过滤掉 null、 undefined、空字符串
    .map(
      (key) => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`
    )
    .join("&");
}

/**
 * 数字格式化函数
 * @param {number} num - 要格式化的数字
 * @param {number} [decimal=1] - 保留的小数位数，默认为1位
 * @param {boolean} [forceDecimal=false] - 是否强制显示小数位
 * @returns {string} 格式化后的字符串
 */
export function formatNumber(num, decimal = 1, forceDecimal = false) {
  // 处理非数字或NaN情况
  if (typeof num !== "number" || isNaN(num)) {
    return "0";
  }

  // 处理负数
  const isNegative = num < 0;
  num = Math.abs(num);

  // 定义单位阈值
  const units = [
    { value: 1e8, suffix: "亿" },
    { value: 1e4, suffix: "w" },
    { value: 1e3, suffix: "k" },
  ];

  // 查找合适的单位
  for (const unit of units) {
    if (num >= unit.value) {
      const formattedNum = num / unit.value;
      // 检查是否需要显示小数部分
      const hasDecimal = formattedNum % 1 !== 0 || forceDecimal;
      const decimalPlaces = hasDecimal ? decimal : 0;

      // 格式化数字并添加单位
      let result = formattedNum.toFixed(decimalPlaces);
      // 去除不必要的.0
      if (!forceDecimal && result.endsWith(".0")) {
        result = result.slice(0, -2);
      }
      return (isNegative ? "-" : "") + result + unit.suffix;
    }
  }

  // 小于所有单位阈值，直接返回原数字
  // 检查是否需要显示小数部分
  const hasDecimal = num % 1 !== 0 || forceDecimal;
  const decimalPlaces = hasDecimal ? decimal : 0;
  let result = num.toFixed(decimalPlaces);
  // 去除不必要的.0
  if (!forceDecimal && result.endsWith(".0")) {
    result = result.slice(0, -2);
  }
  return (isNegative ? "-" : "") + result;
}

export function getAuthorName(list = []) {
  try {
    const names = list.map((item) => item.name);
    return names.join("、") || "--";
  } catch {
    return "--";
  }
}
