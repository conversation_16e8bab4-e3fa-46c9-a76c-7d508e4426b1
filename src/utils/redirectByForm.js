/**
 * 通过表单方式请求重定向接口
 * @param {string} url - 请求地址
 * @param {Object} params - 请求参数
 * @param {string} [method='post'] - 请求方法
 */
export function redirectByForm(url, params, method = 'post') {
  // 创建form元素
  const form = document.createElement('form')
  form.style.display = 'none' // 隐藏表单
  form.action = url
  form.method = method
  form.target = '_self' // 在当前页面打开
  
  // 添加参数
  Object.keys(params).forEach(key => {
    const input = document.createElement('input')
    input.type = 'hidden'
    input.name = key
    input.value = params[key]
    form.appendChild(input)
  })
  
  // 添加到DOM并提交
  document.body.appendChild(form)
  form.submit()
  document.body.removeChild(form)
}