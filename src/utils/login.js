import store from "@/store";
import cookies from "@/utils/cookie";

function watchLogin() {
  // console.log("监听到窗口变化");
  if (document.visibilityState === "visible") {
    // console.log("窗口被激活（用户回到当前标签页）");
    setTimeout(() => {
      store
        .dispatch("user/setUser")
        .then((user) => {
          // console.log(user);
        })
        .catch((err) => {
          console.warn(err);
        });
      if (isLogin()) {
        // console.log("登录成功");
        stopListen();
        location.reload();
      }
    }, 500);
  } else {
    // console.log("窗口被隐藏（用户切换到其他标签页）");
  }
}

function isLogin() {
  const isLogin = store.getters["user/token"];
  // console.log(isLogin);
  if (isLogin) {
    return true;
  }
  return false;
}

function startListen() {
  // console.log("开始监听");
  document.addEventListener("visibilitychange", watchLogin);
}

function stopListen() {
  document.removeEventListener("visibilitychange", watchLogin);
}

function check_login_status() {
  // console.log("登录状态检查");
  store
    .dispatch("user/setUser")
    .then((user) => {
      // console.log("已经登录");
    })
    .catch((err) => {
      console.warn("未登录，开始监听");
      startListen();
    });
}

function login() {
  window.open(process.env.VUE_APP_LOGIN_URL,"_blank");
}

function logout() {
  cookies.remove("USERINFO");
  location.reload();
}

export { login, logout, check_login_status };
