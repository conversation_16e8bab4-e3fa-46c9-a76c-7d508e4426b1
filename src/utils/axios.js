import axios from 'axios'
import store from "@/store";
import { logout } from "@/utils/login"
import { Message } from "element-ui";

const request = axios.create({
  baseURL: process.env.VUE_APP_API_BASE,
  timeout: 60 * 1000 * 30
})

request.interceptors.request.use(
  (config) => {
    config.headers["Authorization"] = store.getters["user/token"] || "";
    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

request.interceptors.response.use(
  (response) => {
    const res = response.data;
    // 这个状态码是和后端约定的
    const { code } = res;
    if (code === undefined) {
      // 如果没有 code 代表这不是标准的后端开发的接口
      return response;
    } else {
      // 有 code 代表这是一个后端接口 可以进行进一步的判断
      switch (code) {        
        case 1:
          return response;
        default:
          Message({
            message: res.message,
            type: "warning",
          });
          return Promise.reject(res);
      }
    }
  },
  (error) => {
    // Do something with response error
    if (error && error.response) {
      switch (error.response.status) {
        case 401:
          _logout()
          break;
        case 400:
          ElMessage.error(error.response.data.message);
          break;
        case 403:
          ElMessage.error(error.response.data.message);
          break;
        default:
          return Promise.reject(error.response);
      }
    }
    return Promise.reject(error);
  },
)

// 退出登录
const _logout = () => {
  Message.error('登录信息过期，请重新登录！');
  logout();
}

export default request
