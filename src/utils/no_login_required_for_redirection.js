/**
 * 免登录跳转统一规划
 */

import store from "@/store";
import { Message } from "element-ui";
const { VUE_APP_SZMY_SJJX_HOST, VUE_APP_SZMY_AI_HOST } = process.env;
//跳转到思政智能体
export function goToSZMYAi(redirectUrl) {
  const token = store.getters["user/token"];
  if (!token) {
    Message({
      type: "warning",
      message: "请先登录!",
    });
    return;
  }
  window.open(
    `${VUE_APP_SZMY_AI_HOST}/nologin/index.html?redirect_uri=${encodeURIComponent(
      redirectUrl
    )}&source=ul&AUTHORIZATION=${token}`
  );
}
//免登录跳转到实践教学管理系统
export function goToSJJX(redirectUrl = "/index.html") {
  const token = store.getters["user/token"];
  if (!token) {
    Message({
      type: "warning",
      message: "请先登录!",
    });
    return;
  }
  window.open(
    `${VUE_APP_SZMY_SJJX_HOST}/nologin/index.html?redirect_uri=${encodeURIComponent(
      redirectUrl
    )}&source=ul&source=ul&AUTHORIZATION=${token}`
  );
}
