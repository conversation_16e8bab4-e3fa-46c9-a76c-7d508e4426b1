@import "./override-element.scss";
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

#app {
  font-family: Avenir, Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

html,
body,
#app {
  height: 100%;
}

.layout-content {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.min-layout-content {
  min-width: 768px;
}

.flex {
  display: flex;
}

.flex-warp {
  flex-wrap: wrap;
}

.flex-center {
  display: flex;
  align-items: center;
}

.flex-center-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-end-between {
  display: flex;
  justify-content: space-between;
  align-items: flex-end;
}

.flex-center-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.flex-start-start {
  display: flex;
  justify-content: flex-start;
  align-items: flex-start;
}
