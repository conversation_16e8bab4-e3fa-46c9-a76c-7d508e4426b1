@import "./element-variables.scss";

/* 修改按钮背景色 */
.el-button--primary {
  display: inline-flex;
  height: var(--Size-Button-Large, 40px);
  padding: var(--Space-Small-8, 8px) var(--Space-20, 20px);
  justify-content: center;
  align-items: center;
  gap: var(--Space-4, 4px);
  flex-shrink: 0;
  color: var(--el-text-color-write);
  font-family: var(--el-font-family);
  font-size: var(--el-font-size-medium);
  font-style: normal;
  font-weight: 400;
  line-height: var(--el-font-line-height-medium); /* 150% */
  border-radius: 8px;
}

.el-pagination.is-background .btn-prev,
.el-pagination.is-background .btn-next,
.el-pagination.is-background .el-pager li {
  border-radius: 6px;
}
