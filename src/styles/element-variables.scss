/* 改变主题色变量 */
$--color-primary: #e60012;

/* 改变 icon 字体路径变量，必需 */
$--font-path: "~element-ui/lib/theme-chalk/fonts";

@import "~element-ui/packages/theme-chalk/src/index";

:root {
  --color-primary: #{$--color-primary};
  --el-border-color-lighter: #{$--border-color-lighter};
  --el-fill-color-light: #f5f7fa;
  ---el-text-color-primary: #182230;
  --el-text-color-secondary: #667085;
  --el-text-color-write: #fff;
  --el-color-white: #fff;
  --el-color-primary: #{$--color-primary};
  --el-font-size-extra-large: 20px;
  --el-font-size-base: 14px;
  --el-font-size-medium: 16px;
  --el-font-size-extra-small: 12px;
  --el-font-line-height-medium: 24px;
  --el-font-line-height-extra-small: 18px;
  --el-font-line-height-base: 20px;
  --el-text-color-regular: #475467;
  --el-font-family: PingFang SC;
  --Size-Button-Large: 40px;
  --Space-Small-8: 8px;
  --Space-20: 20px;
  --Space-4: 4px;
  --Space-Large-16: 16px;
  --el-border-radius-large: 8px;
  --el-fill-color-darker: #e6e8eb;
  --el-text-color-placeholder: #ac7b1c;
}
