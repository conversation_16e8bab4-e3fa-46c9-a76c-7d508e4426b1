{"name": "szmy", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode development", "build": "vue-cli-service build --mode development", "serve:test": "vue-cli-service serve --mode test", "build:test": "vue-cli-service build --mode test", "serve:prod": "vue-cli-service serve --mode production", "build:prod": "vue-cli-service build --mode production"}, "dependencies": {"axios": "^1.11.0", "core-js": "^3.8.3", "echarts": "^6.0.0", "element-ui": "^2.15.14", "js-cookie": "^3.0.5", "svg-sprite-loader": "^6.0.11", "ulearning-obs": "0.0.59", "vue": "^2.6.14", "vue-router": "^3.5.1", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-router": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-plugin-component": "^1.1.1", "sass": "^1.56.1", "sass-loader": "^13.2.0", "vue-loader": "^15.11.1", "vue-template-compiler": "^2.6.14", "webpack": "^5.101.1"}}